<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>CSS Grid</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>

<!--CSS Grid Dev Tools-->
<!--
<div class="container">
    <div class="item">1</div>
    <div class="item">2</div>
    <div class="item">3</div>
    <div class="item">4</div>
    <div class="item">5</div>
    <div class="item">6</div>
    <div class="item">7</div>
    <div class="item">8</div>
    <div class="item">9</div>
    <div class="item">10</div>
</div>
-->

<!--CSS Grid Implicit vs Explicit Tracks-->
<!--
<div class="container">
    <div class="item">1</div>
    <div class="item">2</div>
    <div class="item">3</div>
    <div class="item">4</div>
    <div class="item">5</div>
    <div class="item">6</div>
    <div class="item">7</div>
    <div class="item">8</div>
</div>
-->

<!--CSS grid-auto-flow Explained-->
<!--<div class="container">
    <div class="item">1</div>
    <div class="item">2</div>
    <div class="item">3</div>
    <div class="item">4</div>
    <div class="item">5</div>
    <div class="item">6</div>
    <div class="item">7</div>
</div>-->

<!--Sizing tracks in CSS Grid-->
<!-- <div class="container">
    <div class="item">1</div>
    <div class="item">2</div>
    <div class="item">3</div>
    <div class="item">4</div>
    <div class="item">5</div>
    <div class="item">6</div>
    <div class="item">7</div>
    <div class="item">8</div>
    <div class="item">9</div>
    <div class="item">10</div>
    <div class="item">11</div>
    <div class="item">12</div>
    <div class="item">13</div>
    <div class="item">14</div>
    <div class="item">15</div>
</div> -->

<!--CSS Grid repeat function-->
<!--
<div class="container">
    <div class="item">1</div>
    <div class="item">2</div>
    <div class="item">3</div>
    <div class="item">4</div>
    <div class="item">5</div>
    <div class="item">6</div>
    <div class="item">7</div>
    <div class="item">8</div>
    <div class="item">9</div>
    <div class="item">10</div>
    <div class="item">11</div>
    <div class="item">12</div>
    <div class="item">13</div>
    <div class="item">14</div>
    <div class="item">15</div>
</div>
-->

<!--Sizing Grid items-->
<!--
<div class="container">
    <div class="item item1">1</div>
    <div class="item item2">2</div>
    <div class="item item3">3</div>
    <div class="item item4">4</div>
    <div class="item item5">5</div>
    <div class="item item6">6</div>
    <div class="item item7">7</div>
    <div class="item item8">8</div>
    <div class="item item9">9</div>
    <div class="item item10">10</div>
    <div class="item item11">11</div>
    <div class="item item12">12</div>
    <div class="item item13">13</div>
    <div class="item item14">14</div>
    <div class="item item15">15</div>
    <div class="item item16">16</div>
    <div class="item item17">17</div>
    <div class="item item18">18</div>
    <div class="item item19">19</div>
    <div class="item item20">20</div>
    <div class="item item21">21</div>
    <div class="item item22">22</div>
    <div class="item item23">23</div>
    <div class="item item24">24</div>
    <div class="item item25">25</div>
    <div class="item item26">26</div>
    <div class="item item27">27</div>
    <div class="item item28">28</div>
    <div class="item item29">29</div>
    <div class="item item30">30</div>
</div>
-->

<!--Placing Grid items-->
<div class="container">
    <div class="item item1">1</div>
    <div class="item item2">2</div>
    <div class="item item3">3</div>
    <div class="item item4">4</div>
    <div class="item item5">5</div>
    <div class="item item6">6</div>
    <div class="item item7">7</div>
    <div class="item item8">8</div>
    <div class="item item9">9</div>
    <div class="item item10">10</div>
    <div class="item item11">11</div>
    <div class="item item12">12</div>
    <div class="item item13">13</div>
    <div class="item item14">14</div>
    <div class="item item15">15</div>
    <div class="item item16">16</div>
    <div class="item item17">17</div>
    <div class="item item18">18</div>
    <div class="item item19">19</div>
    <div class="item item20">20</div>
    <div class="item item21">21</div>
    <div class="item item22">22</div>
    <div class="item item23">23</div>
    <div class="item item24">24</div>
    <div class="item item25">25</div>
    <div class="item item26">26</div>
    <div class="item item27">27</div>
    <div class="item item28">28</div>
    <div class="item item29">29</div>
    <div class="item item30">30</div>
</div>

<style>
    /*CSS Grid Implicit vs Explicit Tracks*/
    /*
    .container {
        display: grid;
        grid-gap: 24px;
        grid-template-columns: 200px 400px;
        grid-template-rows: 50px 100px;
        grid-auto-rows: 500px;

    }
    */

    /*CSS grid-auto-flow Explained*/
    /*.container {
        display: grid;
        grid-gap: 24px;
        grid-template-columns: 400px 200px;
        grid-auto-flow: column;
        grid-auto-columns: 200px;

    }
    */

    /*Sizing tracks in CSS Grid*/
    /* .container {
        display: grid;
        height: 600px;
        grid-gap: 24px;
        grid-template-columns: auto 1fr;
        !*grid-template-rows: 1fr 10fr 1fr 5fr 1fr;*!
    }*/

    /*CSS Grid repeat function*/
    /*.container {
        display: grid;
        grid-gap: 24px;
        grid-template-columns: repeat(4, 1fr auto);

    }*/

    /*sizing Grid items*/
    /* .container {
        display: grid;
        grid-gap: 24px;
        grid-template-columns: repeat(5, 1fr);

        .item9{
            !*width: 500px;*!
            grid-column: span 10;
            grid-row: span 2;
            background-color: red;
        }

    }
    */

    /*Placing Grid items*/
    .container {
        display: grid;
        grid-gap: 24px;
        grid-template-columns: repeat(5, 1fr);

        .item9{
            grid-column: 2 / 5;
        }
    }


</style>

</body>
</html>